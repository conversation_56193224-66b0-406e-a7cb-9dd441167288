/* Navigation menu styles */
nav {
    background-color: #444;
    padding: 1rem 0;
    margin-bottom: 2rem;
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.nav-logo img {
    height: 40px;
    width: auto;
}

.nav-menu {
    list-style-type: none;
    margin: 0;
    padding: 0;
    display: flex;
    gap: 2rem;
}

.nav-menu li {
    margin: 0;
}

.nav-menu a {
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    transition: background-color 0.3s ease;
    font-weight: 500;
}

.nav-menu a:hover {
    background-color: #555;
    text-decoration: none;
}

.nav-menu a:active {
    background-color: #777;
}

/* Optional: Add active state for current page */
.nav-menu a.active {
    background-color: #007bff;
}

/* Responsive design for smaller screens */
@media (max-width: 768px) {
    .nav-container {
        flex-direction: column;
        gap: 1rem;
        padding: 0 1rem;
    }

    .nav-menu {
        gap: 1rem;
    }
}

/* Main content area */
.main-content {
    min-height: calc(100vh - 80px);
}

/* Product card styles */
.product-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.product-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.product-image {
    width: 100%;
    height: 200px;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-info {
    padding: 1.5rem;
}

.product-name {
    font-size: 1.25rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.product-description {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 1rem;
}

.product-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.product-price {
    font-size: 1.5rem;
    font-weight: 700;
    color: #4CAF50;
}

.product-category {
    background: #f0f0f0;
    color: #666;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
}

.product-status {
    margin-bottom: 1rem;
}

.stock-status {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.stock-status.in-stock {
    background: #e8f5e8;
    color: #4CAF50;
}

.stock-status.out-of-stock {
    background: #ffeaea;
    color: #f44336;
}

.product-features {
    margin-bottom: 1.5rem;
}

.product-features ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.product-features li {
    padding: 0.25rem 0;
    font-size: 0.85rem;
    color: #666;
    position: relative;
    padding-left: 1rem;
}

.product-features li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #4CAF50;
    font-weight: bold;
}

.product-actions {
    display: flex;
    gap: 0.75rem;
}

.btn {
    padding: 0.75rem 1rem;
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s ease;
    flex: 1;
}

.btn-primary {
    background: #4CAF50;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #45a049;
}

.btn-primary:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.btn-secondary {
    background: #f5f5f5;
    color: #333;
    border: 1px solid #ddd;
}

.btn-secondary:hover {
    background: #e9e9e9;
}
