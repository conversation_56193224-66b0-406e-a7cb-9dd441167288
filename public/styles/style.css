/* Navigation menu styles */
nav {
    background-color: #444;
    padding: 1rem 0;
    margin-bottom: 2rem;
}

nav ul {
    list-style-type: none;
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: center;
    gap: 2rem;
}

nav li {
    margin: 0;
}

nav a {
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    transition: background-color 0.3s ease;
    font-weight: 500;
}

nav a:hover {
    background-color: #555;
    text-decoration: none;
}

nav a:active {
    background-color: #777;
}

/* Optional: Add active state for current page */
nav a.active {
    background-color: #007bff;
}
