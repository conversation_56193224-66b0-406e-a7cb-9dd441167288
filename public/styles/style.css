/* Navigation menu styles */
nav {
    background-color: #444;
    padding: 1rem 0;
    margin-bottom: 2rem;
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.nav-logo img {
    height: 40px;
    width: auto;
}

.nav-menu {
    list-style-type: none;
    margin: 0;
    padding: 0;
    display: flex;
    gap: 2rem;
}

.nav-menu li {
    margin: 0;
}

.nav-menu a {
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    transition: background-color 0.3s ease;
    font-weight: 500;
}

.nav-menu a:hover {
    background-color: #555;
    text-decoration: none;
}

.nav-menu a:active {
    background-color: #777;
}

/* Optional: Add active state for current page */
.nav-menu a.active {
    background-color: #007bff;
}

/* Responsive design for smaller screens */
@media (max-width: 768px) {
    .nav-container {
        flex-direction: column;
        gap: 1rem;
        padding: 0 1rem;
    }

    .nav-menu {
        gap: 1rem;
    }
}
