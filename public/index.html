<!DOCTYPE html>
<html>
    <head>
        <title>NodeJS Express Application</title>
        <style>
            /* Navigation menu styles */
            nav {
                background-color: #333;
                padding: 1rem 0;
                margin-bottom: 2rem;
            }

            nav ul {
                list-style-type: none;
                margin: 0;
                padding: 0;
                display: flex;
                justify-content: center;
                gap: 2rem;
            }

            nav li {
                margin: 0;
            }

            nav a {
                color: white;
                text-decoration: none;
                padding: 0.5rem 1rem;
                border-radius: 4px;
                transition: background-color 0.3s ease;
                font-weight: 500;
            }

            nav a:hover {
                background-color: #555;
                text-decoration: none;
            }

            nav a:active {
                background-color: #777;
            }

            /* Optional: Add active state for current page */
            nav a.active {
                background-color: #007bff;
            }
        </style>
    </head>
    <body>
        <nav>
            <ul>
                <li><a href="/">Home</a></li>
                <li><a href="/products">Products</a></li>
                <li><a href="/products/add">Add Product</a></li>
            </ul>
        </nav>
        <div style="text-align: center;">
            <h1>NodeJS Express Application</h1>
            <img src="https://openjsf.org/_next/image?url=%2Flogo.svg&w=640&q=75" alt="OpenJS Foundation Logo" />
            <p>This is a NodeJS Express application.</p>
        </div>
    </body>
</html>