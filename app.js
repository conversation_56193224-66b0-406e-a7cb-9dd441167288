/*
    NodeJS Express Application
    --------------------------
    We are going to create an Express based Web API that
    highlights some of the following concepts:
      - HTTP methods (GET, POST, PUT, DELETE)
      - Promises (Async/Await)
      - Reading/Writing files
      - Abstractions - separating concerns
*/
const express = require('express');
const app = express();
const port = 3000;

// Our home page uses an HTML file located in
// the public directory
app.use(express.static('public'));

app.get('/', (req, res) => {
  res.send('index.html');
});

app.listen(port, () => {
  console.log(`Example app listening at http://localhost:${port}`);
});
