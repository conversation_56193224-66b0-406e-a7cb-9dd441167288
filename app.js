/*
    NodeJS Express Application
    --------------------------
    We are going to create an Express based Web API that
    highlights some of the following concepts:
      - HTTP methods (GET, POST, PUT, DELETE)
      - Promises (Async/Await)
      - Reading/Writing files
      - Abstractions - separating concerns
*/
const express = require('express');
const ProductsView = require('./views/products');
const app = express();
const port = 3000;

// Our home page uses an HTML file located in
// the public directory
app.use(express.static('public'));

app.get('/', (req, res) => {
  res.send('index.html');
});

// Products page route with filtering and search support
app.get('/products', (req, res) => {
  try {
    console.log('Loading products page...');
    const { category, search } = req.query;
    console.log('Query params:', { category, search });

    // Render the products page using our templating system
    const html = ProductsView.render({ category, search });

    console.log('Products page rendered successfully');
    res.setHeader('Content-Type', 'text/html');
    res.send(html);
  } catch (error) {
    console.error('Error rendering products page:', error);
    console.error('Error stack:', error.stack);
    res.status(500).send(`
      <html>
        <head><title>Error - Express Products</title></head>
        <body>
          <h1>Internal Server Error</h1>
          <p>Unable to load products page. Please check the server logs.</p>
          <p><a href="/">Return to Home</a></p>
        </body>
      </html>
    `);
  }
});

app.listen(port, () => {
  console.log(`Example app listening at http://localhost:${port}`);
});
