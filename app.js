/*
    NodeJS Express Application
    --------------------------
    We are going to create an Express based Web API that
    highlights some of the following concepts:
      - HTTP methods (GET, POST, PUT, DELETE)
      - Promises (Async/Await)
      - Reading/Writing files
      - Abstractions - separating concerns
*/
const express = require('express');
const fs = require('fs');
const path = require('path');
const ProductsView = require('./views/products');
const AddProductView = require('./views/add-product');
const app = express();
const port = 3000;

// Middleware to parse form data
app.use(express.urlencoded({ extended: true }));
app.use(express.json());

// Our home page uses an HTML file located in
// the public directory
app.use(express.static('public'));

app.get('/', (req, res) => {
  res.send('index.html');
});

// Products page route with filtering and search support
app.get('/products', (req, res) => {
  try {
    console.log('Loading products page...');
    const { category, search } = req.query;
    console.log('Query params:', { category, search });

    // Render the products page using our templating system
    const html = ProductsView.render({ category, search });

    console.log('Products page rendered successfully');
    res.setHeader('Content-Type', 'text/html');
    res.send(html);
  } catch (error) {
    console.error('Error rendering products page:', error);
    console.error('Error stack:', error.stack);
    res.status(500).send(`
      <html>
        <head><title>Error - Express Products</title></head>
        <body>
          <h1>Internal Server Error</h1>
          <p>Unable to load products page. Please check the server logs.</p>
          <p><a href="/">Return to Home</a></p>
        </body>
      </html>
    `);
  }
});

// Add Product page - GET route
app.get('/products/add', (req, res) => {
  try {
    const html = AddProductView.render();
    res.setHeader('Content-Type', 'text/html');
    res.send(html);
  } catch (error) {
    console.error('Error rendering add product page:', error);
    res.status(500).send('Internal Server Error');
  }
});

// Add Product - POST route
app.post('/products/add', (req, res) => {
  try {
    const { name, price, description, category, customCategory, image, inStock, features } = req.body;
    const errors = [];

    // Validation
    if (!name || name.trim().length === 0) {
      errors.push('Product name is required');
    }
    if (!price || isNaN(parseFloat(price)) || parseFloat(price) < 0) {
      errors.push('Valid price is required');
    }
    if (!description || description.trim().length === 0) {
      errors.push('Product description is required');
    }
    if (!category || (category === 'custom' && (!customCategory || customCategory.trim().length === 0))) {
      errors.push('Product category is required');
    }

    // If there are validation errors, re-render the form with errors
    if (errors.length > 0) {
      const html = AddProductView.render({
        formData: req.body,
        errors: errors
      });
      res.setHeader('Content-Type', 'text/html');
      res.send(html);
      return;
    }

    // Load existing products
    const productsPath = path.join(__dirname, 'data/products.json');
    const productsData = fs.readFileSync(productsPath, 'utf8');
    const products = JSON.parse(productsData);

    // Generate new product ID
    const newId = Math.max(...products.map(p => p.id), 0) + 1;

    // Process features (split by newlines and filter empty)
    const featuresArray = features ?
      features.split('\n').map(f => f.trim()).filter(f => f.length > 0) :
      [];

    // Create new product object
    const newProduct = {
      id: newId,
      name: name.trim(),
      description: description.trim(),
      price: parseFloat(price),
      category: category === 'custom' ? customCategory.trim() : category,
      inStock: inStock === 'true',
      image: image && image.trim() ? image.trim() : `/images/product.svg`,
      features: featuresArray
    };

    // Add to products array
    products.push(newProduct);

    // Save back to file
    fs.writeFileSync(productsPath, JSON.stringify(products, null, 2));

    console.log('New product added:', newProduct);

    // Render success page
    const html = AddProductView.render({
      successMessage: `Product "${newProduct.name}" has been successfully added to the catalog!`
    });
    res.setHeader('Content-Type', 'text/html');
    res.send(html);

  } catch (error) {
    console.error('Error adding product:', error);
    res.status(500).send('Internal Server Error');
  }
});

app.listen(port, () => {
  console.log(`Example app listening at http://localhost:${port}`);
});
