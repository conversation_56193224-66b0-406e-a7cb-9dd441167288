const fs = require('fs');
const path = require('path');
const Template = require('../utils/template');

/**
 * Products page view controller
 */
class ProductsView {
    /**
     * Load products data from JSON file
     * @returns {Array} Array of products
     */
    static loadProducts() {
        try {
            const productsPath = path.join(__dirname, '../data/products.json');
            const productsData = fs.readFileSync(productsPath, 'utf8');
            return JSON.parse(productsData);
        } catch (error) {
            console.error('Error loading products:', error);
            return [];
        }
    }

    /**
     * Render the products page
     * @param {Object} options - Rendering options
     * @param {string} options.category - Filter by category (optional)
     * @param {string} options.search - Search term (optional)
     * @returns {string} Complete HTML page
     */
    static render(options = {}) {
        const { category, search } = options;
        let products = this.loadProducts();

        // Filter products if category is specified
        if (category) {
            products = products.filter(product => 
                product.category.toLowerCase() === category.toLowerCase()
            );
        }

        // Filter products if search term is specified
        if (search) {
            const searchTerm = search.toLowerCase();
            products = products.filter(product =>
                product.name.toLowerCase().includes(searchTerm) ||
                product.description.toLowerCase().includes(searchTerm) ||
                product.category.toLowerCase().includes(searchTerm)
            );
        }

        // Get unique categories for filter dropdown
        const allProducts = this.loadProducts();
        const categories = [...new Set(allProducts.map(product => product.category))];

        const content = `
        <div class="products-page">
            <div class="products-header">
                <h1>Our Products</h1>
                <p>Discover our wide range of quality products with express delivery.</p>
            </div>

            <div class="products-filters">
                <div class="filter-group">
                    <label for="category-filter">Filter by Category:</label>
                    <select id="category-filter" onchange="filterByCategory(this.value)">
                        <option value="">All Categories</option>
                        ${categories.map(cat => 
                            `<option value="${cat}" ${category === cat ? 'selected' : ''}>${cat}</option>`
                        ).join('')}
                    </select>
                </div>
                
                <div class="filter-group">
                    <label for="search-input">Search Products:</label>
                    <input type="text" id="search-input" placeholder="Search products..." 
                           value="${search || ''}" onkeyup="searchProducts(this.value)">
                </div>
            </div>

            <div class="products-stats">
                <p>Showing ${products.length} product${products.length !== 1 ? 's' : ''}</p>
            </div>

            <div class="products-grid">
                ${products.length > 0 ? 
                    products.map(product => Template.renderProductCard(product)).join('') :
                    '<div class="no-products"><p>No products found matching your criteria.</p></div>'
                }
            </div>
        </div>`;

        const additionalCSS = `
            <style>
                .products-page {
                    max-width: 1200px;
                    margin: 0 auto;
                    padding: 2rem;
                }
                
                .products-header {
                    text-align: center;
                    margin-bottom: 2rem;
                }
                
                .products-header h1 {
                    color: #333;
                    margin-bottom: 0.5rem;
                }
                
                .products-filters {
                    display: flex;
                    gap: 2rem;
                    margin-bottom: 2rem;
                    padding: 1rem;
                    background: #f5f5f5;
                    border-radius: 8px;
                }
                
                .filter-group {
                    display: flex;
                    flex-direction: column;
                    gap: 0.5rem;
                }
                
                .filter-group label {
                    font-weight: 500;
                    color: #333;
                }
                
                .filter-group select,
                .filter-group input {
                    padding: 0.5rem;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    font-size: 14px;
                }
                
                .products-stats {
                    margin-bottom: 1rem;
                    color: #666;
                }
                
                .products-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
                    gap: 2rem;
                }
                
                .no-products {
                    grid-column: 1 / -1;
                    text-align: center;
                    padding: 2rem;
                    color: #666;
                }
            </style>`;

        const additionalJS = `
            <script>
                function filterByCategory(category) {
                    const url = new URL(window.location);
                    if (category) {
                        url.searchParams.set('category', category);
                    } else {
                        url.searchParams.delete('category');
                    }
                    window.location.href = url.toString();
                }
                
                function searchProducts(searchTerm) {
                    clearTimeout(window.searchTimeout);
                    window.searchTimeout = setTimeout(() => {
                        const url = new URL(window.location);
                        if (searchTerm.trim()) {
                            url.searchParams.set('search', searchTerm.trim());
                        } else {
                            url.searchParams.delete('search');
                        }
                        window.location.href = url.toString();
                    }, 500);
                }
            </script>`;

        return Template.renderPage({
            title: 'Products - Express Products',
            content: content,
            activeRoute: '/products',
            additionalCSS: additionalCSS,
            additionalJS: additionalJS
        });
    }
}

module.exports = ProductsView;
