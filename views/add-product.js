const fs = require('fs');
const path = require('path');
const Template = require('../utils/template');

/**
 * Add Product page view controller
 */
class AddProductView {
    /**
     * Get available categories from existing products
     * @returns {Array} Array of unique categories
     */
    static getCategories() {
        try {
            const productsPath = path.join(__dirname, '../data/products.json');
            const productsData = fs.readFileSync(productsPath, 'utf8');
            const products = JSON.parse(productsData);
            const categories = [...new Set(products.map(product => product.category))];
            return categories.sort();
        } catch (error) {
            console.error('Error loading categories:', error);
            return ['Electronics', 'Health & Fitness', 'Furniture', 'Home & Office'];
        }
    }

    /**
     * Render the add product page
     * @param {Object} options - Rendering options
     * @param {Object} options.formData - Pre-filled form data (for validation errors)
     * @param {Array} options.errors - Validation errors
     * @param {string} options.successMessage - Success message after adding product
     * @returns {string} Complete HTML page
     */
    static render(options = {}) {
        const { formData = {}, errors = [], successMessage = '' } = options;
        const categories = this.getCategories();

        const content = `
        <div class="add-product-page">
            <div class="add-product-header">
                <h1>Add New Product</h1>
                <p>Add a new product to the Express Products catalog.</p>
            </div>

            ${successMessage ? `
                <div class="success-message">
                    <p>${successMessage}</p>
                </div>
            ` : ''}

            ${errors.length > 0 ? `
                <div class="error-messages">
                    <h3>Please fix the following errors:</h3>
                    <ul>
                        ${errors.map(error => `<li>${error}</li>`).join('')}
                    </ul>
                </div>
            ` : ''}

            <form class="add-product-form" method="POST" action="/products/add">
                <div class="form-row">
                    <div class="form-group">
                        <label for="name">Product Name *</label>
                        <input type="text" id="name" name="name" value="${formData.name || ''}" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="price">Price ($) *</label>
                        <input type="number" id="price" name="price" step="0.01" min="0" value="${formData.price || ''}" required>
                    </div>
                </div>

                <div class="form-group">
                    <label for="description">Description *</label>
                    <textarea id="description" name="description" rows="4" required>${formData.description || ''}</textarea>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="category">Category *</label>
                        <select id="category" name="category" required>
                            <option value="">Select a category</option>
                            ${categories.map(category => 
                                `<option value="${category}" ${formData.category === category ? 'selected' : ''}>${category}</option>`
                            ).join('')}
                            <option value="custom">Add New Category...</option>
                        </select>
                    </div>
                    
                    <div class="form-group" id="custom-category-group" style="display: none;">
                        <label for="customCategory">New Category</label>
                        <input type="text" id="customCategory" name="customCategory" value="${formData.customCategory || ''}">
                    </div>
                </div>

                <div class="form-group">
                    <label for="image">Image URL</label>
                    <input type="url" id="image" name="image" value="${formData.image || ''}" placeholder="/images/product-name.svg">
                    <small>Leave empty to use a default placeholder image</small>
                </div>

                <div class="form-group">
                    <label for="inStock">Stock Status</label>
                    <div class="radio-group">
                        <label class="radio-label">
                            <input type="radio" name="inStock" value="true" ${formData.inStock !== 'false' ? 'checked' : ''}>
                            In Stock
                        </label>
                        <label class="radio-label">
                            <input type="radio" name="inStock" value="false" ${formData.inStock === 'false' ? 'checked' : ''}>
                            Out of Stock
                        </label>
                    </div>
                </div>

                <div class="form-group">
                    <label for="features">Product Features</label>
                    <div class="features-input">
                        <textarea id="features" name="features" rows="6" placeholder="Enter each feature on a new line">${formData.features || ''}</textarea>
                        <small>Enter each feature on a separate line</small>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">Add Product</button>
                    <button type="reset" class="btn btn-secondary">Clear Form</button>
                    <a href="/products" class="btn btn-outline">Cancel</a>
                </div>
            </form>
        </div>`;

        const additionalCSS = `
            <style>
                .add-product-page {
                    max-width: 800px;
                    margin: 0 auto;
                    padding: 2rem;
                }
                
                .add-product-header {
                    text-align: center;
                    margin-bottom: 2rem;
                }
                
                .add-product-header h1 {
                    color: #333;
                    margin-bottom: 0.5rem;
                }
                
                .success-message {
                    background: #e8f5e8;
                    color: #4CAF50;
                    padding: 1rem;
                    border-radius: 8px;
                    margin-bottom: 2rem;
                    border: 1px solid #4CAF50;
                }
                
                .error-messages {
                    background: #ffeaea;
                    color: #f44336;
                    padding: 1rem;
                    border-radius: 8px;
                    margin-bottom: 2rem;
                    border: 1px solid #f44336;
                }
                
                .error-messages ul {
                    margin: 0.5rem 0 0 1rem;
                }
                
                .add-product-form {
                    background: white;
                    padding: 2rem;
                    border-radius: 12px;
                    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                }
                
                .form-row {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 1rem;
                }
                
                .form-group {
                    margin-bottom: 1.5rem;
                }
                
                .form-group label {
                    display: block;
                    font-weight: 600;
                    color: #333;
                    margin-bottom: 0.5rem;
                }
                
                .form-group input,
                .form-group select,
                .form-group textarea {
                    width: 100%;
                    padding: 0.75rem;
                    border: 2px solid #ddd;
                    border-radius: 6px;
                    font-size: 1rem;
                    transition: border-color 0.3s ease;
                }
                
                .form-group input:focus,
                .form-group select:focus,
                .form-group textarea:focus {
                    outline: none;
                    border-color: #4CAF50;
                }
                
                .form-group small {
                    display: block;
                    color: #666;
                    font-size: 0.85rem;
                    margin-top: 0.25rem;
                }
                
                .radio-group {
                    display: flex;
                    gap: 1rem;
                }
                
                .radio-label {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    font-weight: normal;
                    cursor: pointer;
                }
                
                .radio-label input[type="radio"] {
                    width: auto;
                }
                
                .form-actions {
                    display: flex;
                    gap: 1rem;
                    justify-content: flex-end;
                    margin-top: 2rem;
                    padding-top: 2rem;
                    border-top: 1px solid #eee;
                }
                
                .btn-outline {
                    background: transparent;
                    color: #666;
                    border: 2px solid #ddd;
                    text-decoration: none;
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                }
                
                .btn-outline:hover {
                    background: #f5f5f5;
                    text-decoration: none;
                }
                
                @media (max-width: 768px) {
                    .form-row {
                        grid-template-columns: 1fr;
                    }
                    
                    .form-actions {
                        flex-direction: column;
                    }
                }
            </style>`;

        const additionalJS = `
            <script>
                // Show/hide custom category input
                document.getElementById('category').addEventListener('change', function() {
                    const customGroup = document.getElementById('custom-category-group');
                    const customInput = document.getElementById('customCategory');
                    
                    if (this.value === 'custom') {
                        customGroup.style.display = 'block';
                        customInput.required = true;
                    } else {
                        customGroup.style.display = 'none';
                        customInput.required = false;
                        customInput.value = '';
                    }
                });
                
                // Auto-generate image URL based on product name
                document.getElementById('name').addEventListener('input', function() {
                    const imageInput = document.getElementById('image');
                    if (!imageInput.value) {
                        const slug = this.value.toLowerCase()
                            .replace(/[^a-z0-9]+/g, '-')
                            .replace(/^-+|-+$/g, '');
                        if (slug) {
                            imageInput.value = '/images/' + slug + '.svg';
                        }
                    }
                });
            </script>`;

        return Template.renderPage({
            title: 'Add Product - Express Products',
            content: content,
            activeRoute: '/products/add',
            additionalCSS: additionalCSS,
            additionalJS: additionalJS
        });
    }
}

module.exports = AddProductView;
