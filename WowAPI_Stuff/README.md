# World of Warcraft: Mists of Pandaria AH API Addon

The purpose of this application is to create an addon for World of Warcraft that will allow users to view the auction house data for their characters.

The data will be pulled from the World of Warcraft API and displayed in a user-friendly format. Settings for which items to display or search will be based on a config that we will need to design.

Some initial thoughts around the config are going to be based on my own current needs:

Engineering leveling materials based on [WoW Professions: Engineering Leveling Guide MOP Classic](https://www.wow-professions.com/mop/engineering-leveling-guide-mop-classic).

Also, since we are coming into phase 1 raids, and people are gearing, the Darkmoon Decks are quite valuable. I've been keeping an eye on [Tiger Deck](https://www.wowhead.com/mop-classic/item=79323/tiger-deck). I regularly scan the AH for prices on this, as well as [Two of Tigers](), [Three of Tigers](), [Four of Tigers](), [Five of Tigers](), [Six of Tigers](), [Seven of Tigers](), [Eight of Tigers](), and [Ace of Tigers](), and finally the [Relic of Xuen](https://www.wowhead.com/mop-classic/item=79328/relic-of-xuen), which can be either Agility or Strength, and is the result of the [Tiger Deck](https://www.wowhead.com/mop-classic/item=79323/tiger-deck).  
  
What I'd like to be able to do is input lists of items of value, materials to craft or create such items, and have the addon display the data for me. I'd also like to be able to compare prices over time. The idea is to be able to quickly and easily tell if it is worth buying materials on the AH to craft and list items for sale, or if it is better to wait for the price to drop.

## Connecting to the World of Warcraft API
TODO: These should not be shared, and should be stored securely.  
Client Credentials: `9921916341f949efac99283df2b2f6d6`  
Client Secret: `o0Xxndd2JaooCgCd6Qf7WY5JapdPpUy3`  
Access Token: `USBBR0gWrOSkr2l6kFnhlKrcBkIBTBHAkU`  
Usage:   
```shell 
# request
curl -u {client_id}:{client_secret} -d grant_type=client_credentials https://oauth.battle.net/token
```

```shell
# response
{"access_token":"USBBR0gWrOSkr2l6kFnhlKrcBkIBTBHAkU","token_type":"bearer","expires_in":86399,"sub":"9921916341f949efac99283df2b2f6d6"}
```

```shell
# get realm list
curl -i -H "Authorization: Bearer USBBR0gWrOSkr2l6kFnhlKrcBkIBTBHAkU" -H "Region: us" -H "Battlenet-Namespace: dynamic-classic-us" -X GET https://us.api.blizzard.com/data/wow/connected-realm/index
```

