const fs = require('fs');
const path = require('path');

/**
 * Simple templating utility for Express Products
 */
class Template {
    /**
     * Generate the common navigation HTML
     * @param {string} activeRoute - The currently active route for highlighting
     * @returns {string} Navigation HTML
     */
    static getNavigation(activeRoute = '') {
        return `
        <nav>
            <div class="nav-container">
                <div class="nav-logo">
                    <img src="/images/logo.svg" alt="Express Products Logo" />
                </div>
                <ul class="nav-menu">
                    <li><a href="/" ${activeRoute === '/' ? 'class="active"' : ''}>Home</a></li>
                    <li><a href="/products" ${activeRoute === '/products' ? 'class="active"' : ''}>Products</a></li>
                    <li><a href="/products/add" ${activeRoute === '/products/add' ? 'class="active"' : ''}>Add Product</a></li>
                </ul>
            </div>
        </nav>`;
    }

    /**
     * Generate the common HTML head section
     * @param {string} title - Page title
     * @param {string} additionalCSS - Additional CSS files to include
     * @returns {string} Head HTML
     */
    static getHead(title = 'Express Products', additionalCSS = '') {
        return `
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>${title}</title>
            <link rel="stylesheet" href="/styles/style.css">
            ${additionalCSS}
        </head>`;
    }

    /**
     * Generate a complete HTML page with navigation
     * @param {Object} options - Page options
     * @param {string} options.title - Page title
     * @param {string} options.content - Main page content
     * @param {string} options.activeRoute - Active navigation route
     * @param {string} options.additionalCSS - Additional CSS
     * @param {string} options.additionalJS - Additional JavaScript
     * @returns {string} Complete HTML page
     */
    static renderPage(options = {}) {
        const {
            title = 'Express Products',
            content = '',
            activeRoute = '',
            additionalCSS = '',
            additionalJS = ''
        } = options;

        return `<!DOCTYPE html>
<html lang="en">
${this.getHead(title, additionalCSS)}
<body>
${this.getNavigation(activeRoute)}
    <main class="main-content">
        ${content}
    </main>
    ${additionalJS}
</body>
</html>`;
    }

    /**
     * Render a product card HTML
     * @param {Object} product - Product object
     * @returns {string} Product card HTML
     */
    static renderProductCard(product) {
        const stockStatus = product.inStock ? 
            '<span class="stock-status in-stock">In Stock</span>' : 
            '<span class="stock-status out-of-stock">Out of Stock</span>';

        return `
        <div class="product-card" data-id="${product.id}">
            <div class="product-image">
                <img src="${product.image}" alt="${product.name}" />
            </div>
            <div class="product-info">
                <h3 class="product-name">${product.name}</h3>
                <p class="product-description">${product.description}</p>
                <div class="product-details">
                    <span class="product-price">$${product.price.toFixed(2)}</span>
                    <span class="product-category">${product.category}</span>
                </div>
                <div class="product-status">
                    ${stockStatus}
                </div>
                <div class="product-features">
                    <ul>
                        ${product.features.map(feature => `<li>${feature}</li>`).join('')}
                    </ul>
                </div>
                <div class="product-actions">
                    <button class="btn btn-primary" ${!product.inStock ? 'disabled' : ''}>
                        ${product.inStock ? 'Add to Cart' : 'Out of Stock'}
                    </button>
                    <button class="btn btn-secondary">View Details</button>
                </div>
            </div>
        </div>`;
    }
}

module.exports = Template;
